import 'dart:developer' as developer;
import 'package:koc_app/src/shared/cache/automated_cache_clearing_service.dart';
import 'package:koc_app/src/shared/cache/cache_endpoint_registry.dart';

/// Mixin that provides automated cache clearing capabilities to cubits
/// This eliminates the need for manual endpoint declarations and provides
/// a consistent interface for cache management across the application.
///
/// Usage:
/// ```dart
/// class MyCubit extends BaseCubit<MyState> with CacheClearingMixin {
///   Future<void> pullToRefresh() async {
///     try {
///       await clearCacheForPullToRefresh();
///       await initState(); // Your data loading logic
///     } catch (e) {
///       handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
///     }
///   }
/// }
/// ```
mixin CacheClearingMixin {
  /// Get the automated cache clearing service instance
  AutomatedCacheClearingService get _cacheService => AutomatedCacheClearingService();

  // ============================================================================
  // PULL-TO-REFRESH CACHE CLEARING
  // ============================================================================

  /// Clear cache for pull-to-refresh operations
  /// This is the main method that should be used in pullToRefresh implementations
  /// It automatically handles both global and site-specific endpoints
  Future<void> clearCacheForPullToRefresh() async {
    try {
      developer.log('🔄 [$runtimeType] Starting pull-to-refresh cache clearing');
      await _cacheService.clearCacheForPullToRefresh();
      developer.log('✅ [$runtimeType] Pull-to-refresh cache clearing completed');
    } catch (e) {
      developer.log('❌ [$runtimeType] Pull-to-refresh cache clearing failed: $e');
      rethrow;
    }
  }

  // ============================================================================
  // SCENARIO-BASED CACHE CLEARING
  // ============================================================================

  /// Clear cache for profile/account updates
  /// Use this when user profile data changes
  Future<void> clearCacheForProfileUpdate() async {
    await _clearCacheForScenario(CacheClearingScenario.profileUpdate);
  }

  /// Clear cache for payment-related updates
  /// Use this when payment data or settings change
  Future<void> clearCacheForPaymentUpdate() async {
    await _clearCacheForScenario(CacheClearingScenario.paymentUpdate);
  }

  /// Clear cache for voucher-related updates
  /// Use this when voucher data changes
  Future<void> clearCacheForVoucherUpdate() async {
    await _clearCacheForScenario(CacheClearingScenario.voucherUpdate);
  }

  /// Clear all global (non-site-specific) cache endpoints
  /// Use this for comprehensive cache clearing
  Future<void> clearAllGlobalCache() async {
    await _clearCacheForScenario(CacheClearingScenario.allGlobal);
  }

  // ============================================================================
  // SPECIALIZED CACHE CLEARING
  // ============================================================================

  /// Clear cache for voucher operations
  /// This includes both global voucher endpoints and site-specific voucher data
  Future<void> clearCacheForVoucherOperations() async {
    try {
      developer.log('🔄 [$runtimeType] Starting voucher operations cache clearing');
      await _cacheService.clearCacheForVoucherOperations();
      developer.log('✅ [$runtimeType] Voucher operations cache clearing completed');
    } catch (e) {
      developer.log('❌ [$runtimeType] Voucher operations cache clearing failed: $e');
      rethrow;
    }
  }

  /// Clear cache for campaign operations
  /// This includes campaign summaries, featured campaigns, etc.
  Future<void> clearCacheForCampaignOperations() async {
    try {
      developer.log('🔄 [$runtimeType] Starting campaign operations cache clearing');
      await _cacheService.clearCacheForCampaignOperations();
      developer.log('✅ [$runtimeType] Campaign operations cache clearing completed');
    } catch (e) {
      developer.log('❌ [$runtimeType] Campaign operations cache clearing failed: $e');
      rethrow;
    }
  }

  /// Clear cache when switching between sites
  /// This ensures data consistency when users change their active site
  Future<void> clearCacheForSiteSwitch(int newSiteId, {int? previousSiteId}) async {
    try {
      developer.log('🔄 [$runtimeType] Starting site switch cache clearing');
      await _cacheService.clearCacheForSiteSwitch(newSiteId, previousSiteId: previousSiteId);
      developer.log('✅ [$runtimeType] Site switch cache clearing completed');
    } catch (e) {
      developer.log('❌ [$runtimeType] Site switch cache clearing failed: $e');
      rethrow;
    }
  }

  // ============================================================================
  // INTERNAL HELPER METHODS
  // ============================================================================

  /// Internal method to clear cache for a specific scenario
  Future<void> _clearCacheForScenario(CacheClearingScenario scenario) async {
    try {
      developer.log('🔄 [$runtimeType] Starting cache clearing for scenario: ${scenario.displayName}');
      await _cacheService.clearCacheForScenario(scenario);
      developer.log('✅ [$runtimeType] Cache clearing completed for scenario: ${scenario.displayName}');
    } catch (e) {
      developer.log('❌ [$runtimeType] Cache clearing failed for scenario ${scenario.displayName}: $e');
      rethrow;
    }
  }

  // ============================================================================
  // UTILITY AND DEBUG METHODS
  // ============================================================================

  /// Get information about what endpoints would be cleared for a scenario
  /// Useful for debugging and testing
  Future<Map<String, dynamic>> getCacheScenarioInfo(CacheClearingScenario scenario) async {
    return await _cacheService.getScenarioInfo(scenario);
  }

  /// Get debug information about the cache clearing capabilities
  Map<String, dynamic> getCacheClearingDebugInfo() {
    return {
      'cubit': runtimeType.toString(),
      'service': _cacheService.getDebugInfo(),
      'availableMethods': [
        'clearCacheForPullToRefresh',
        'clearCacheForProfileUpdate',
        'clearCacheForPaymentUpdate',
        'clearCacheForVoucherUpdate',
        'clearAllGlobalCache',
        'clearCacheForVoucherOperations',
        'clearCacheForCampaignOperations',
        'clearCacheForSiteSwitch',
      ],
    };
  }

  /// Validate that the cache clearing service is properly configured
  Future<bool> validateCacheService() async {
    return await _cacheService.validateServices();
  }

  // ============================================================================
  // LEGACY COMPATIBILITY METHODS
  // ============================================================================

  /// Legacy method for backward compatibility
  /// @deprecated Use clearCacheForPullToRefresh() instead
  @Deprecated('Use clearCacheForPullToRefresh() instead')
  Future<void> clearPullToRefreshCache() async {
    await clearCacheForPullToRefresh();
  }

  /// Legacy method for backward compatibility
  /// @deprecated Use clearCacheForVoucherOperations() instead
  @Deprecated('Use clearCacheForVoucherOperations() instead')
  Future<void> clearVoucherCache() async {
    await clearCacheForVoucherOperations();
  }
}

/// Extension to provide additional cache clearing utilities
extension CacheClearingExtension on CacheClearingMixin {
  /// Quick method to clear cache and log the operation
  /// Useful for one-liner cache clearing with automatic error handling
  Future<void> safeClearCache(
    Future<void> Function() clearOperation, {
    String? operationName,
  }) async {
    final operation = operationName ?? 'cache clearing';
    try {
      developer.log('🔄 [$runtimeType] Starting $operation');
      await clearOperation();
      developer.log('✅ [$runtimeType] $operation completed successfully');
    } catch (e) {
      developer.log('❌ [$runtimeType] $operation failed: $e');
      // Don't rethrow to make it "safe" - just log the error
    }
  }
}
