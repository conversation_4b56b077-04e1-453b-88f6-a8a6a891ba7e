import 'dart:developer' as developer;
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/shared/cache/cache_endpoint_registry.dart';
import 'package:koc_app/src/shared/services/api_service.dart';
import 'package:koc_app/src/shared/services/shared_preferences_service.dart';

/// Automated cache clearing service that provides high-level cache management
/// operations based on predefined scenarios and endpoint categories.
///
/// This service eliminates the need for manual endpoint declarations in
/// pullToRefresh methods and other cache clearing operations.
class AutomatedCacheClearingService {
  static final AutomatedCacheClearingService _instance = AutomatedCacheClearingService._internal();
  factory AutomatedCacheClearingService() => _instance;
  AutomatedCacheClearingService._internal();

  /// Get the API service instance
  ApiService get _apiService => Modular.get<ApiService>();

  /// Get the shared preferences service instance
  SharedPreferencesService get _sharedPreferencesService => Modular.get<SharedPreferencesService>();

  // ============================================================================
  // HIGH-LEVEL CACHE CLEARING METHODS
  // ============================================================================

  /// Clear cache for pull-to-refresh operations
  /// This method automatically clears both global and site-specific endpoints
  /// that are relevant for refreshing data in pull-to-refresh scenarios.
  Future<void> clearCacheForPullToRefresh() async {
    try {
      developer.log('🔄 Starting automated cache clearing for pull-to-refresh');

      final siteId = await _sharedPreferencesService.getCurrentSiteId();

      // Clear global endpoints for pull-to-refresh
      await _clearEndpointsByScenario(CacheClearingScenario.pullToRefresh);

      // Clear site-specific cache if siteId is available
      if (siteId != null) {
        await _clearSiteSpecificCache(siteId);
        developer.log('✅ Site-specific cache cleared for siteId: $siteId');
      } else {
        developer.log('⚠️ No siteId available, skipping site-specific cache clearing');
      }

      developer.log('✅ Pull-to-refresh cache clearing completed successfully');
    } catch (e) {
      developer.log('❌ Error during pull-to-refresh cache clearing: $e');
      rethrow;
    }
  }

  /// Clear cache when switching between sites
  /// This ensures data consistency when users change their active site
  Future<void> clearCacheForSiteSwitch(int newSiteId, {int? previousSiteId}) async {
    try {
      developer.log('🔄 Starting automated cache clearing for site switch to: $newSiteId');

      // Clear cache for the previous site if provided
      if (previousSiteId != null && previousSiteId != newSiteId) {
        await _clearSiteSpecificCache(previousSiteId);
        developer.log('✅ Previous site cache cleared for siteId: $previousSiteId');
      }

      // Clear cache for the new site to ensure fresh data
      await _clearSiteSpecificCache(newSiteId);

      // Also clear global endpoints that might be affected by site changes
      await _clearEndpointsByScenario(CacheClearingScenario.profileUpdate);

      developer.log('✅ Site switch cache clearing completed for siteId: $newSiteId');
    } catch (e) {
      developer.log('❌ Error during site switch cache clearing: $e');
      rethrow;
    }
  }

  /// Clear cache for specific scenarios (profile update, payment update, etc.)
  Future<void> clearCacheForScenario(CacheClearingScenario scenario) async {
    try {
      developer.log('🔄 Starting automated cache clearing for scenario: ${scenario.displayName}');

      await _clearEndpointsByScenario(scenario);

      developer.log('✅ Cache clearing completed for scenario: ${scenario.displayName}');
    } catch (e) {
      developer.log('❌ Error during cache clearing for scenario ${scenario.displayName}: $e');
      rethrow;
    }
  }

  /// Clear cache for voucher-related operations
  /// This is commonly used in voucher pages and related functionality
  Future<void> clearCacheForVoucherOperations() async {
    try {
      developer.log('🔄 Starting automated cache clearing for voucher operations');

      final siteId = await _sharedPreferencesService.getCurrentSiteId();

      // Clear voucher-related global endpoints
      await _clearEndpointsByScenario(CacheClearingScenario.voucherUpdate);

      // Clear site-specific voucher endpoints if siteId is available
      if (siteId != null) {
        await _clearSpecificSiteEndpoint(siteId, '/v3/publishers/me/sites/{siteId}/vouchers');
      }

      developer.log('✅ Voucher operations cache clearing completed');
    } catch (e) {
      developer.log('❌ Error during voucher operations cache clearing: $e');
      rethrow;
    }
  }

  /// Clear cache for campaign-related operations
  /// This handles campaign data, featured campaigns, etc.
  Future<void> clearCacheForCampaignOperations() async {
    try {
      developer.log('🔄 Starting automated cache clearing for campaign operations');

      final siteId = await _sharedPreferencesService.getCurrentSiteId();

      if (siteId != null) {
        // Clear campaign-related site-specific endpoints
        final campaignEndpoints = [
          '/v3/publishers/me/sites/{siteId}/campaigns/featured-summary',
          '/v3/publishers/me/sites/{siteId}/campaigns/top-summary',
          '/v3/publishers/me/sites/{siteId}/campaigns/fastest-growing-summary',
          '/v3/publishers/me/sites/{siteId}/campaigns/count-summary',
        ];

        for (final endpointTemplate in campaignEndpoints) {
          await _clearSpecificSiteEndpoint(siteId, endpointTemplate);
        }

        developer.log('✅ Campaign operations cache clearing completed for siteId: $siteId');
      } else {
        developer.log('⚠️ No siteId available, skipping campaign cache clearing');
      }
    } catch (e) {
      developer.log('❌ Error during campaign operations cache clearing: $e');
      rethrow;
    }
  }

  // ============================================================================
  // INTERNAL HELPER METHODS
  // ============================================================================

  /// Clear endpoints for a specific scenario
  Future<void> _clearEndpointsByScenario(CacheClearingScenario scenario) async {
    final endpoints = CacheEndpointRegistry.getEndpointsForScenario(scenario);

    for (final endpoint in endpoints) {
      try {
        await _apiService.clearCacheForEndpoint(endpoint);
        developer.log('🧹 Cleared cache for endpoint: $endpoint');
      } catch (e) {
        developer.log('⚠️ Failed to clear cache for endpoint $endpoint: $e');
        // Continue with other endpoints even if one fails
      }
    }

    developer.log('✅ Cleared ${endpoints.length} endpoints for scenario: ${scenario.displayName}');
  }

  /// Clear site-specific cache using the existing automated method
  Future<void> _clearSiteSpecificCache(int siteId) async {
    await _apiService.clearSiteSpecificCache(siteId);
  }

  /// Clear a specific site endpoint by substituting the siteId
  Future<void> _clearSpecificSiteEndpoint(int siteId, String endpointTemplate) async {
    final endpoint = endpointTemplate.replaceAll('{siteId}', siteId.toString());
    await _apiService.clearCacheForEndpoint(endpoint);
    developer.log('🧹 Cleared site-specific endpoint: $endpoint');
  }

  // ============================================================================
  // UTILITY AND DEBUG METHODS
  // ============================================================================

  /// Get information about what endpoints would be cleared for a scenario
  /// Useful for debugging and testing
  Future<Map<String, dynamic>> getScenarioInfo(CacheClearingScenario scenario) async {
    final globalEndpoints = CacheEndpointRegistry.getEndpointsForScenario(scenario);
    final siteId = await _sharedPreferencesService.getCurrentSiteId();

    return {
      'scenario': scenario.displayName,
      'globalEndpoints': globalEndpoints,
      'siteId': siteId,
      'siteSpecificEndpoints': siteId != null ? CacheEndpointRegistry.getSiteSpecificEndpoints(siteId) : [],
      'totalEndpoints':
          globalEndpoints.length + (siteId != null ? CacheEndpointRegistry.getSiteSpecificTemplates().length : 0),
    };
  }

  /// Get comprehensive debug information about the cache clearing service
  Map<String, dynamic> getDebugInfo() {
    return {
      'service': 'AutomatedCacheClearingService',
      'registry': CacheEndpointRegistry.getDebugInfo(),
      'availableScenarios': CacheClearingScenario.values.map((s) => s.displayName).toList(),
    };
  }

  /// Validate that all required services are available
  Future<bool> validateServices() async {
    try {
      Modular.get<ApiService>();
      Modular.get<SharedPreferencesService>();
      return true;
    } catch (e) {
      developer.log('❌ Service validation failed: $e');
      return false;
    }
  }
}
