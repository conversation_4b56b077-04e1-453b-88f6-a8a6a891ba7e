# Automated Cache Clearing System

This document explains the new automated cache clearing system that eliminates the need for manual endpoint declarations in `pullToRefresh` methods and other cache clearing operations.

## Overview

The automated cache clearing system consists of three main components:

1. **CacheEndpointRegistry** - Centralized registry of all cache endpoints organized by categories and scenarios
2. **AutomatedCacheClearingService** - Service that provides high-level cache clearing operations
3. **CacheClearingMixin** - Mixin that provides easy integration for cubits

## Benefits

- ✅ **Eliminates manual endpoint declarations** - No more hardcoded endpoint lists
- ✅ **Reduces errors** - Prevents missing or incorrect endpoint declarations
- ✅ **Improves maintainability** - Centralized endpoint management
- ✅ **Leverages existing patterns** - Uses the existing `clearSiteSpecificCache()` method
- ✅ **Consistent behavior** - Standardized cache clearing across the app
- ✅ **Easy to use** - Simple mixin integration for cubits

## Quick Start

### 1. Update Your Cubit

Add the `CacheClearingMixin` to your cubit:

```dart
import 'package:koc_app/src/shared/cache/cache_clearing_mixin.dart';

class MyCubit extends BaseCubit<MyState> with CacheClearingMixin {
  // Your cubit implementation
}
```

### 2. Update pullToRefresh Method

Replace manual cache clearing with automated approach:

**Before:**
```dart
Future<void> pullToRefresh() async {
  try {
    final apiService = Modular.get<ApiService>();
    final siteId = await _sharedPreferencesService.getCurrentSiteId();

    if (siteId != null) {
      await apiService.clearCacheForEndpoint('/v3/publishers/me/notifications');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/reports/summary');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/reports/daily');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/vouchers');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/featured-summary');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sso-key');
    }

    await initState();
  } catch (e) {
    handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
  }
}
```

**After:**
```dart
Future<void> pullToRefresh() async {
  try {
    // Automated cache clearing - handles both global and site-specific endpoints
    await clearCacheForPullToRefresh();

    await initState();
  } catch (e) {
    handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
  }
}
```

## Available Methods

### General Cache Clearing

- `clearCacheForPullToRefresh()` - For pull-to-refresh operations
- `clearCacheForProfileUpdate()` - When user profile data changes
- `clearCacheForPaymentUpdate()` - When payment data changes
- `clearCacheForVoucherUpdate()` - When voucher data changes
- `clearAllGlobalCache()` - Clear all global (non-site-specific) endpoints

### Specialized Cache Clearing

- `clearCacheForVoucherOperations()` - For voucher-related operations
- `clearCacheForCampaignOperations()` - For campaign-related operations
- `clearCacheForSiteSwitch(newSiteId, previousSiteId)` - When switching sites

## How It Works

### 1. Endpoint Categories

Endpoints are organized into categories in `CacheEndpointRegistry`:

- **Global Endpoints** - Don't depend on siteId (e.g., `/v3/publishers/me/notifications`)
- **Site-Specific Endpoints** - Require siteId substitution (e.g., `/v3/publishers/me/sites/{siteId}/vouchers`)
- **Critical Endpoints** - High priority for immediate clearing
- **Background Clearable** - Can be cleared with lower priority

### 2. Scenario-Based Clearing

Different scenarios have predefined endpoint sets:

- **Pull-to-Refresh** - Endpoints that should be refreshed during pull-to-refresh
- **Profile Update** - Endpoints affected by profile changes
- **Payment Update** - Payment-related endpoints
- **Voucher Update** - Voucher-related endpoints

### 3. Automatic Site Handling

The system automatically:
- Checks if siteId is available
- Uses existing `clearSiteSpecificCache(siteId)` for site-specific endpoints
- Substitutes `{siteId}` placeholders in endpoint templates
- Skips site-specific clearing if siteId is null (with proper logging)

## Endpoint Registry

### Adding New Endpoints

To add new endpoints to the registry, update `CacheEndpointRegistry`:

```dart
// Add to appropriate category
static const List<String> globalEndpoints = [
  '/v3/publishers/me/notifications',
  '/v3/publishers/me/new-endpoint', // Add here
];

// Or add site-specific endpoint template
static const List<String> siteSpecificEndpointTemplates = [
  '/v3/publishers/me/sites/{siteId}/vouchers',
  '/v3/publishers/me/sites/{siteId}/new-feature', // Add here
];
```

### Creating New Scenarios

Add new scenarios to `CacheClearingScenario` enum and update the registry:

```dart
enum CacheClearingScenario {
  pullToRefresh,
  profileUpdate,
  paymentUpdate,
  voucherUpdate,
  allGlobal,
  newScenario, // Add here
}

// Add corresponding endpoint list
static const List<String> newScenarioEndpoints = [
  '/v3/publishers/me/endpoint1',
  '/v3/publishers/me/endpoint2',
];
```

## Migration Guide

### For Existing Cubits

1. Add the mixin to your cubit class
2. Replace manual cache clearing in `pullToRefresh` with `clearCacheForPullToRefresh()`
3. Remove unused imports (`flutter_modular`, `api_service` if only used for cache clearing)
4. Test the functionality

### For New Cubits

1. Extend `BaseCubit` with `CacheClearingMixin`
2. Use appropriate cache clearing methods based on your use case
3. No need to manually manage endpoints

## Debugging

### Debug Information

Get information about cache clearing scenarios:

```dart
// Get scenario information
final info = await getCacheScenarioInfo(CacheClearingScenario.pullToRefresh);
print(info);

// Get debug information
final debugInfo = getCacheClearingDebugInfo();
print(debugInfo);
```

### Validation

Validate that services are properly configured:

```dart
final isValid = await validateCacheService();
if (!isValid) {
  // Handle service configuration issues
}
```

## Best Practices

1. **Use scenario-specific methods** when possible (e.g., `clearCacheForVoucherOperations()` for voucher pages)
2. **Use `clearCacheForPullToRefresh()`** for general pull-to-refresh operations
3. **Don't mix manual and automated clearing** in the same method
4. **Test cache clearing behavior** after migration
5. **Monitor logs** to ensure proper cache clearing

## Logging

The system provides comprehensive logging:

- 🔄 Starting cache clearing operations
- ✅ Successful cache clearing
- ❌ Failed cache clearing with error details
- ⚠️ Warnings (e.g., no siteId available)
- 🧹 Individual endpoint clearing

## Backward Compatibility

The system maintains backward compatibility:

- Existing `clearCacheForEndpoint()` and `clearSiteSpecificCache()` methods still work
- Legacy methods are marked as `@Deprecated` with migration guidance
- No breaking changes to existing API

## Performance

The automated system provides:

- **Better performance** - Leverages existing `clearSiteSpecificCache()` for bulk operations
- **Reduced API calls** - Eliminates redundant individual endpoint clearing
- **Optimized clearing** - Only clears relevant endpoints for each scenario
