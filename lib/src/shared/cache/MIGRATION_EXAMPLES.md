# Migration Examples for Automated Cache Clearing

This document provides concrete examples of how to migrate existing cubits to use the new automated cache clearing system.

## Example 1: Home Cubit (Already Migrated)

### Before
```dart
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/shared/services/api_service.dart';

class HomeCubit extends BaseCubit<HomeState> {
  Future<void> pullToRefresh() async {
    try {
      final apiService = Modular.get<ApiService>();
      final siteId = await _sharedPreferencesService.getCurrentSiteId();

      if (siteId != null) {
        await apiService.clearCacheForEndpoint('/v3/publishers/me/notifications');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/reports/summary');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/reports/daily');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/vouchers');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/featured-summary');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/sso-key');
      }

      await initState();
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
```

### After
```dart
import 'package:koc_app/src/shared/cache/cache_clearing_mixin.dart';

class HomeCubit extends BaseCubit<HomeState> with CacheClearingMixin {
  Future<void> pullToRefresh() async {
    try {
      // Automated cache clearing - handles both global and site-specific endpoints
      await clearCacheForPullToRefresh();

      await initState();
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
```

## Example 2: Voucher Cubit (Already Migrated)

### Before
```dart
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/shared/services/api_service.dart';

class VoucherCubit extends BaseCubit<VoucherState> {
  Future<void> pullToRefresh() async {
    try {
      emit(state.copyWith(isPullToRefresh: true));

      final apiService = Modular.get<ApiService>();
      final siteId = await commonCubit.sharedPreferencesService.getCurrentSiteId();

      if (siteId != null) {
        await apiService.clearCacheForEndpoint('/v3/publishers/me/vouchers');
        await apiService.clearCacheForEndpoint('/v3/publishers/me/voucher-categories');
      }

      await Future.wait([
        _refreshVouchersForPullToRefresh(),
        _refreshVoucherCategoriesForPullToRefresh(),
      ]);

      if (state.tabIndex > 0) {
        await _refreshCurrentTabForPullToRefresh();
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }
}
```

### After
```dart
import 'package:koc_app/src/shared/cache/cache_clearing_mixin.dart';

class VoucherCubit extends BaseCubit<VoucherState> with CacheClearingMixin {
  Future<void> pullToRefresh() async {
    try {
      emit(state.copyWith(isPullToRefresh: true));

      // Use automated cache clearing for voucher operations
      await clearCacheForVoucherOperations();

      await Future.wait([
        _refreshVouchersForPullToRefresh(),
        _refreshVoucherCategoriesForPullToRefresh(),
      ]);

      if (state.tabIndex > 0) {
        await _refreshCurrentTabForPullToRefresh();
      }
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }
}
```

## Example 3: Account Cubit Migration

### Before
```dart
import 'package:flutter_modular/flutter_modular.dart';
import 'package:koc_app/src/shared/services/api_service.dart';

class AccountCubit extends BaseCubit<AccountState> {
  Future<void> pullToRefresh() async {
    try {
      emit(state.copyWith(isPullToRefresh: true));

      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/account');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites');

      await Future.wait([
        getAccount(),
        getTrafficSources(),
      ]);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }

  Future<void> _clearInterestedFieldsRelatedCaches(int siteId) async {
    try {
      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/categories');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/featured-summary');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/top-summary');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/sites/$siteId/campaigns/fastest-growing-summary');
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
```

### After
```dart
import 'package:koc_app/src/shared/cache/cache_clearing_mixin.dart';

class AccountCubit extends BaseCubit<AccountState> with CacheClearingMixin {
  Future<void> pullToRefresh() async {
    try {
      emit(state.copyWith(isPullToRefresh: true));

      // Use automated cache clearing for profile-related operations
      await clearCacheForProfileUpdate();

      await Future.wait([
        getAccount(),
        getTrafficSources(),
      ]);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }

  Future<void> _clearInterestedFieldsRelatedCaches(int siteId) async {
    try {
      // Use automated cache clearing for campaign operations
      await clearCacheForCampaignOperations();
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
```

## Example 4: Payment Report Cubit Migration

### Before
```dart
class PaymentReportCubit extends BaseCubit<PaymentReportState> {
  Future<void> pullToRefresh() async {
    try {
      emit(state.copyWith(isPullToRefresh: true));

      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/payment-summary');
      await apiService.clearCacheForEndpoint('/v3/publishers/countries/minimum-payment-details');
      await apiService.clearCacheForEndpoint('/v3/publishers/me/payment');

      final now = DateTime.now();
      final fromMonth = DateTime(now.year - 1, now.month);
      final toMonth = now;

      await _refreshPaymentDataForPullToRefresh(fromMonth, toMonth);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }
}
```

### After
```dart
import 'package:koc_app/src/shared/cache/cache_clearing_mixin.dart';

class PaymentReportCubit extends BaseCubit<PaymentReportState> with CacheClearingMixin {
  Future<void> pullToRefresh() async {
    try {
      emit(state.copyWith(isPullToRefresh: true));

      // Use automated cache clearing for payment operations
      await clearCacheForPaymentUpdate();

      final now = DateTime.now();
      final fromMonth = DateTime(now.year - 1, now.month);
      final toMonth = now;

      await _refreshPaymentDataForPullToRefresh(fromMonth, toMonth);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    } finally {
      emit(state.copyWith(isPullToRefresh: false));
    }
  }
}
```

## Example 5: Simple Report Cubit Migration

### Before
```dart
class ConversionReportCubit extends BaseCubit<ConversionReportState> {
  Future<void> pullToRefresh(DateTime fromDate, DateTime toDate, 
      ReportQueryPeriodBase periodBase, int page, int limit,
      ConversionStatus? status, Item? campaign, Item? site) async {
    try {
      final apiService = Modular.get<ApiService>();

      await apiService.clearCacheForEndpoint('/v3/publishers/me/reports/conversion-summary');

      await findConversions(fromDate, toDate, periodBase, page, limit, status, campaign, site);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
```

### After
```dart
import 'package:koc_app/src/shared/cache/cache_clearing_mixin.dart';

class ConversionReportCubit extends BaseCubit<ConversionReportState> with CacheClearingMixin {
  Future<void> pullToRefresh(DateTime fromDate, DateTime toDate, 
      ReportQueryPeriodBase periodBase, int page, int limit,
      ConversionStatus? status, Item? campaign, Item? site) async {
    try {
      // Use automated cache clearing for pull-to-refresh
      await clearCacheForPullToRefresh();

      await findConversions(fromDate, toDate, periodBase, page, limit, status, campaign, site);
    } catch (e) {
      handleError(e, (message) => emit(state.copyWith(errorMessage: message)));
    }
  }
}
```

## Migration Checklist

For each cubit you migrate:

- [ ] Add `import 'package:koc_app/src/shared/cache/cache_clearing_mixin.dart';`
- [ ] Add `with CacheClearingMixin` to the class declaration
- [ ] Replace manual cache clearing with appropriate automated method:
  - `clearCacheForPullToRefresh()` for general pull-to-refresh
  - `clearCacheForVoucherOperations()` for voucher-related operations
  - `clearCacheForCampaignOperations()` for campaign-related operations
  - `clearCacheForProfileUpdate()` for account/profile operations
  - `clearCacheForPaymentUpdate()` for payment operations
- [ ] Remove unused imports (`flutter_modular`, `api_service` if only used for cache clearing)
- [ ] Test the functionality
- [ ] Verify logs show proper cache clearing behavior

## Method Selection Guide

Choose the appropriate method based on your cubit's purpose:

| Cubit Type | Recommended Method |
|------------|-------------------|
| Home/Dashboard | `clearCacheForPullToRefresh()` |
| Voucher | `clearCacheForVoucherOperations()` |
| Campaign | `clearCacheForCampaignOperations()` |
| Account/Profile | `clearCacheForProfileUpdate()` |
| Payment | `clearCacheForPaymentUpdate()` |
| Reports (general) | `clearCacheForPullToRefresh()` |
| Site switching | `clearCacheForSiteSwitch(newSiteId, previousSiteId)` |

## Testing Your Migration

After migration, verify:

1. **Pull-to-refresh works** - Data is refreshed properly
2. **Cache is cleared** - Check logs for cache clearing messages
3. **No errors** - No missing endpoint errors
4. **Performance** - Cache clearing is fast and efficient
5. **Site switching** - Site-specific data is properly cleared
